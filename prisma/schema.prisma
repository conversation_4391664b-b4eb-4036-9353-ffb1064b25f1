generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Brand {
  id          String      @id @default(cuid())
  name        String      @unique @db.VarChar(100)
  description String?
  logo        String?     @db.VarChar(500)
  status      BrandStatus @default(ACTIVE)
  createdAt   DateTime    @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime    @updatedAt @db.Timestamptz(6)
  createdBy   String      @db.VarChar(50)
  projects    Project[]

  @@index([status])
  @@index([name])
  @@map("brands")
}

model Project {
  id                        String                @id @default(cuid())
  documentType              DocumentType          @default(PROJECT_INITIATION)
  brandId                   String
  projectName               String                @unique @db.VarChar(200)
  startDate                 DateTime?             @db.Timestamptz(6)
  endDate                   DateTime?             @db.Timestamptz(6)
  planningBudget            Decimal               @db.Decimal(15, 2)
  influencerBudget          Decimal               @db.Decimal(15, 2)
  adBudget                  Decimal               @db.Decimal(15, 2)
  otherBudget               Decimal               @db.Decimal(15, 2)
  expectedPaymentMonth      String?               @db.VarChar(7)
  paymentTermDays           Int?                  @db.SmallInt
  influencerCost            Decimal               @db.Decimal(15, 2)
  adCost                    Decimal               @db.Decimal(15, 2)
  otherCost                 Decimal               @db.Decimal(15, 2)
  estimatedInfluencerRebate Decimal               @db.Decimal(15, 2)
  executorPM                String                @db.VarChar(50)
  contentMediaIds           String[]
  contractType              ContractType
  settlementRules           String
  kpi                       String
  status                    ProjectStatus         @default(DRAFT)
  createdAt                 DateTime              @default(now()) @db.Timestamptz(6)
  updatedAt                 DateTime              @updatedAt @db.Timestamptz(6)
  createdBy                 String                @db.VarChar(50)
  updatedBy                 String                @db.VarChar(50)
  contractSigningStatus     ContractSigningStatus @default(PENDING)
  metadata                  Json?
  intermediaryCost          Decimal               @default(0) @db.Decimal(15, 2)
  attachments               Attachment[]
  changeLogs                ProjectChangeLog[]
  revenues                  ProjectRevenue[]
  brand                     Brand                 @relation(fields: [brandId], references: [id])
  budgets                   Budget[]
  weeklyBudgets             WeeklyBudget[]

  @@index([brandId, status])
  @@index([executorPM])
  @@index([contractType])
  @@index([contractSigningStatus])
  @@index([startDate, endDate])
  @@index([createdAt])
  @@index([status])
  @@index([expectedPaymentMonth])
  @@index([paymentTermDays])
  @@map("projects")
}

model Attachment {
  id           String   @id @default(cuid())
  filename     String   @db.VarChar(255)
  originalName String   @db.VarChar(255)
  size         BigInt
  mimeType     String   @db.VarChar(100)
  url          String   @db.VarChar(500)
  projectId    String?
  uploadedBy   String   @db.VarChar(50)
  uploadedAt   DateTime @default(now()) @db.Timestamptz(6)
  project      Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([uploadedBy])
  @@map("attachments")
}

model ProjectChangeLog {
  id            String     @id @default(cuid())
  changeType    ChangeType @default(UPDATE)
  changeTitle   String     @db.VarChar(200)
  changeDetails Json?
  beforeData    Json?
  afterData     Json?
  changedFields String[]
  operatorId    String     @db.VarChar(50)
  operatorName  String     @db.VarChar(100)
  operatorIP    String?    @db.VarChar(45)
  userAgent     String?    @db.VarChar(500)
  reason        String?
  description   String?
  projectId     String
  createdAt     DateTime   @default(now()) @db.Timestamptz(6)
  project       Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, createdAt])
  @@index([operatorId])
  @@index([changeType])
  @@index([createdAt])
  @@map("project_change_logs")
}

model ProjectRevenue {
  id            String        @id @default(cuid())
  title         String        @db.VarChar(200)
  revenueType   RevenueType   @default(PROJECT_INCOME)
  status        RevenueStatus @default(RECEIVING)
  plannedAmount Decimal       @db.Decimal(15, 2)
  actualAmount  Decimal?      @db.Decimal(15, 2)
  invoiceAmount Decimal?      @db.Decimal(15, 2)
  plannedDate   DateTime?     @db.Timestamptz(6)
  confirmedDate DateTime?     @db.Timestamptz(6)
  invoiceDate   DateTime?     @db.Timestamptz(6)
  receivedDate  DateTime?     @db.Timestamptz(6)
  milestone     String?       @db.VarChar(200)
  invoiceNumber String?       @db.VarChar(100)
  paymentTerms  String?
  notes         String?
  projectId     String
  createdAt     DateTime      @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime      @updatedAt @db.Timestamptz(6)
  createdBy     String        @db.VarChar(50)
  updatedBy     String        @db.VarChar(50)
  project       Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, status])
  @@index([plannedDate])
  @@index([status])
  @@index([revenueType])
  @@index([createdAt])
  @@map("project_revenues")
}

// 预算表 - 重构后的预算模型（去掉周的概念）
model Budget {
  id              String       @id @default(cuid())
  title           String       @db.VarChar(200)
  serviceType     ServiceType
  serviceContent  String
  remarks         String?
  contractAmount  Decimal      @db.Decimal(15, 2)
  taxRate         TaxRate
  totalPaidAmount Decimal      @default(0) @db.Decimal(15, 2) // 总已付金额
  remainingAmount Decimal      @db.Decimal(15, 2)
  status          BudgetStatus @default(CREATED)
  projectId       String
  supplierId      String?
  createdAt       DateTime     @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime     @updatedAt @db.Timestamptz(6)
  createdBy       String       @db.VarChar(50)
  updatedBy       String       @db.VarChar(50)

  // 关联关系
  fundingPlans FundingPlan[]
  project      Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  supplier     Supplier?     @relation(fields: [supplierId], references: [id])

  @@index([projectId])
  @@index([supplierId])
  @@index([serviceType])
  @@index([status])
  @@index([createdAt])
  @@map("budgets")
}

// 资金计划表 - 新增的资金计划模型
model FundingPlan {
  id              String            @id @default(cuid())
  title           String            @db.VarChar(200)
  year            Int               @db.SmallInt
  month           Int               @db.SmallInt // 月份 (1-12)
  weekOfMonth     Int               @db.SmallInt // 当月第几周 (1-5)
  plannedAmount   Decimal           @db.Decimal(15, 2) // 计划资金
  paidAmount      Decimal           @default(0) @db.Decimal(15, 2) // 已付款
  remainingAmount Decimal           @db.Decimal(15, 2) // 剩余金额
  status          FundingPlanStatus @default(SUBMITTED)
  approvalStatus  ApprovalStatus    @default(NONE)
  approvalAmount  Decimal?          @db.Decimal(15, 2)
  approvalReason  String?
  remarks         String?
  budgetId        String
  createdAt       DateTime          @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime          @updatedAt @db.Timestamptz(6)
  createdBy       String            @db.VarChar(50)
  updatedBy       String            @db.VarChar(50)

  // 关联关系
  approvalInstances ApprovalInstance[]
  budget            Budget             @relation(fields: [budgetId], references: [id], onDelete: Cascade)

  @@unique([budgetId, year, month, weekOfMonth]) // 确保同一预算下的年月周组合唯一
  @@index([budgetId])
  @@index([year, month, weekOfMonth])
  @@index([status])
  @@index([approvalStatus])
  @@index([createdAt])
  @@map("funding_plans")
}

// 周预算表 - 保留以兼容现有代码
model WeeklyBudget {
  id              String             @id @default(cuid())
  title           String             @db.VarChar(200)
  weekStartDate   DateTime?          @db.Timestamptz(6)
  weekEndDate     DateTime?          @db.Timestamptz(6)
  weekNumber      Int?               @db.SmallInt
  year            Int?               @db.SmallInt
  serviceType     ServiceType
  serviceContent  String
  remarks         String?
  contractAmount  Decimal            @db.Decimal(15, 2)
  taxRate         TaxRate
  paidAmount      Decimal            @default(0) @db.Decimal(15, 2)
  remainingAmount Decimal            @db.Decimal(15, 2)
  status          WeeklyBudgetStatus @default(CREATED)
  approvalStatus  ApprovalStatus     @default(NONE)
  approvalAmount  Decimal?           @db.Decimal(15, 2)
  approvalReason  String?
  projectId       String
  supplierId      String?
  createdAt       DateTime           @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime           @updatedAt @db.Timestamptz(6)
  createdBy       String             @db.VarChar(50)
  updatedBy       String             @db.VarChar(50)

  // 关联关系
  approvalInstances ApprovalInstance[]
  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  supplier          Supplier?          @relation(fields: [supplierId], references: [id])

  @@index([projectId])
  @@index([supplierId])
  @@index([serviceType])
  @@index([status])
  @@index([approvalStatus])
  @@index([createdAt])
  @@map("weekly_budgets")
}

model Supplier {
  id               String         @id @default(cuid())
  name             String         @db.VarChar(200)
  shortName        String?        @db.VarChar(100)
  code             String?        @unique @db.VarChar(50)
  contactPerson    String?        @db.VarChar(100)
  contactPhone     String?        @db.VarChar(20)
  contactEmail     String?        @db.VarChar(100)
  address          String?
  taxNumber        String?        @db.VarChar(50)
  bankAccount      String?        @db.VarChar(50)
  bankName         String?        @db.VarChar(200)
  legalPerson      String?        @db.VarChar(100)
  serviceTypes     ServiceType[]
  preferredTaxRate TaxRate?
  creditLimit      Decimal?       @db.Decimal(15, 2)
  paymentTerms     String?
  status           SupplierStatus @default(ACTIVE)
  rating           Int?           @db.SmallInt
  notes            String?
  createdAt        DateTime       @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime       @updatedAt @db.Timestamptz(6)
  createdBy        String         @db.VarChar(50)
  updatedBy        String         @db.VarChar(50)
  budgets          Budget[]
  weeklyBudgets    WeeklyBudget[]

  @@index([name])
  @@index([status])
  @@index([serviceTypes])
  @@index([createdAt])
  @@map("suppliers")
}

model User {
  userid               String     @id @db.VarChar(50)
  unionid              String?    @db.VarChar(100)
  name                 String     @db.VarChar(100)
  avatar               String?    @db.VarChar(500)
  stateCode            String?    @db.VarChar(10)
  managerUserid        String?    @db.VarChar(50)
  mobile               String?    @db.VarChar(20)
  hideMobile           Boolean?   @default(false)
  telephone            String?    @db.VarChar(20)
  jobNumber            String?    @db.VarChar(50)
  title                String?    @db.VarChar(100)
  email                String?    @db.VarChar(100)
  workPlace            String?    @db.VarChar(200)
  remark               String?
  loginId              String?    @db.VarChar(100)
  exclusiveAccountType String?    @db.VarChar(20)
  exclusiveAccount     Boolean?   @default(false)
  deptIdList           Int[]
  extension            String?
  hiredDate            DateTime?  @db.Timestamptz(6)
  active               Boolean?   @default(true)
  realAuthed           Boolean?   @default(false)
  orgEmail             String?    @db.VarChar(100)
  orgEmailType         String?    @db.VarChar(50)
  senior               Boolean?   @default(false)
  admin                Boolean?   @default(false)
  boss                 Boolean?   @default(false)
  lastSyncAt           DateTime   @default(now()) @db.Timestamptz(6)
  isActive             Boolean    @default(true)
  userRoles            UserRole[]

  @@index([name])
  @@index([isActive])
  @@index([mobile])
  @@index([email])
  @@index([managerUserid])
  @@map("users")
}

model Department {
  deptId                Int              @id
  name                  String           @db.VarChar(200)
  parentId              Int              @default(1)
  createDeptGroup       Boolean          @default(false)
  autoAddUser           Boolean          @default(false)
  fromUnionOrg          Boolean          @default(false)
  tags                  String?          @db.VarChar(500)
  order                 Int              @default(0)
  deptManagerUseridList String[]
  outerDept             Boolean          @default(false)
  outerPermitDepts      Int[]
  outerPermitUsers      String[]
  orgDeptOwner          String?          @db.VarChar(50)
  deptPerimits          Int              @default(0)
  userPerimits          Int              @default(0)
  outerDeptOnlySelf     Boolean          @default(false)
  sourceIdentifier      String?          @db.VarChar(100)
  ext                   String?
  hideSceneConfig       Json?
  lastSyncAt            DateTime         @default(now()) @db.Timestamptz(6)
  departmentRoles       DepartmentRole[]

  @@index([name])
  @@index([parentId])
  @@index([order])
  @@index([lastSyncAt])
  @@map("departments")
}

model Role {
  id              String           @id @default(cuid())
  name            String           @unique @db.VarChar(100)
  displayName     String           @db.VarChar(100)
  description     String?
  isSystem        Boolean          @default(false)
  isActive        Boolean          @default(true)
  createdAt       DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime         @updatedAt @db.Timestamptz(6)
  createdBy       String           @db.VarChar(50)
  departmentRoles DepartmentRole[]
  rolePermissions RolePermission[]
  userRoles       UserRole[]

  @@index([name])
  @@index([isActive])
  @@index([isSystem])
  @@map("roles")
}

model Permission {
  id              String           @id @default(cuid())
  name            String           @unique @db.VarChar(100)
  displayName     String           @db.VarChar(100)
  description     String?
  module          String           @db.VarChar(50)
  action          String           @db.VarChar(50)
  resource        String?          @db.VarChar(50)
  isSystem        Boolean          @default(false)
  createdAt       DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime         @updatedAt @db.Timestamptz(6)
  rolePermissions RolePermission[]

  @@index([module])
  @@index([action])
  @@index([name])
  @@map("permissions")
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime   @default(now()) @db.Timestamptz(6)
  createdBy    String     @db.VarChar(50)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
  @@map("role_permissions")
}

model UserRole {
  id        String    @id @default(cuid())
  userid    String    @db.VarChar(50)
  roleId    String
  createdAt DateTime  @default(now()) @db.Timestamptz(6)
  createdBy String    @db.VarChar(50)
  expiresAt DateTime? @db.Timestamptz(6)
  role      Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userid], references: [userid], onDelete: Cascade)

  @@unique([userid, roleId])
  @@index([userid])
  @@index([roleId])
  @@index([expiresAt])
  @@map("user_roles")
}

model DepartmentRole {
  id         String     @id @default(cuid())
  deptId     Int
  roleId     String
  createdAt  DateTime   @default(now()) @db.Timestamptz(6)
  createdBy  String     @db.VarChar(50)
  expiresAt  DateTime?  @db.Timestamptz(6)
  department Department @relation(fields: [deptId], references: [deptId], onDelete: Cascade)
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([deptId, roleId])
  @@index([deptId])
  @@index([roleId])
  @@index([expiresAt])
  @@map("department_roles")
}

model ApprovalInstance {
  id                String         @id @default(cuid())
  processInstanceId String         @unique @db.VarChar(100)
  processCode       String         @db.VarChar(100)
  businessId        String?        @db.VarChar(100)
  title             String         @db.VarChar(200)
  originatorUserId  String         @db.VarChar(50)
  status            ApprovalStatus @default(PENDING)
  result            String?        @db.VarChar(50)
  createTime        DateTime       @db.Timestamptz(6)
  finishTime        DateTime?      @db.Timestamptz(6)
  approvalAmount    Decimal        @db.Decimal(15, 2)
  actualAmount      Decimal?       @db.Decimal(15, 2)
  reason            String?
  remark            String?

  // 新的关联关系 - 资金计划（重构后）
  fundingPlanId String?
  fundingPlan   FundingPlan? @relation(fields: [fundingPlanId], references: [id], onDelete: Cascade)

  // 保留的关联关系 - 周预算（兼容性）
  weeklyBudgetId String?
  weeklyBudget   WeeklyBudget? @relation(fields: [weeklyBudgetId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  @@index([processInstanceId])
  @@index([fundingPlanId])
  @@index([weeklyBudgetId])
  @@index([status])
  @@index([originatorUserId])
  @@index([createTime])
  @@map("approval_instances")
}

enum BrandStatus {
  ACTIVE
  INACTIVE

  @@map("brand_status")
}

enum DocumentType {
  PROJECT_INITIATION
  PROJECT_PROPOSAL
  PROJECT_PLAN
  PROJECT_EXECUTION
  PROJECT_SUMMARY

  @@map("document_type")
}

enum ContractType {
  ANNUAL_FRAME
  QUARTERLY_FRAME
  SINGLE
  PO_ORDER
  JING_TASK

  @@map("contract_type")
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  COMPLETED
  CANCELLED

  @@map("project_status")
}

enum RevenueStatus {
  RECEIVING
  RECEIVED
  CANCELLED

  @@map("revenue_status")
}

enum RevenueType {
  INFLUENCER_INCOME
  PROJECT_INCOME
  OTHER

  @@map("revenue_type")
}

enum SupplierStatus {
  ACTIVE
  INACTIVE
  PENDING
  BLACKLISTED

  @@map("supplier_status")
}

enum ServiceType {
  INFLUENCER
  ADVERTISING
  OTHER

  @@map("service_type")
}

enum TaxRate {
  SPECIAL_1
  SPECIAL_3
  SPECIAL_6
  GENERAL
  NONE
  NO_TAX

  @@map("tax_rate")
}

enum BudgetStatus {
  CREATED
  APPROVED
  EXECUTING
  COMPLETED
  CANCELLED

  @@map("budget_status")
}

enum WeeklyBudgetStatus {
  CREATED
  APPROVED
  EXECUTING
  COMPLETED
  CANCELLED

  @@map("weekly_budget_status")
}

enum FundingPlanStatus {
  DRAFT
  SUBMITTED
  APPROVED
  EXECUTING
  COMPLETED
  CANCELLED

  @@map("funding_plan_status")
}

enum ApprovalStatus {
  NONE
  PENDING
  APPROVED
  REJECTED
  CANCELLED

  @@map("approval_status")
}

enum ContractSigningStatus {
  NO_CONTRACT
  SIGNED
  SIGNING
  PENDING

  @@map("contract_signing_status")
}

enum ChangeType {
  CREATE
  UPDATE
  DELETE
  STATUS_CHANGE
  APPROVAL
  ATTACHMENT

  @@map("change_type")
}
