# 批量创建资金计划 API 文档

## 接口概述

批量创建资金计划接口允许一次性创建多个资金计划，采用**严格事务模式**：任何一个计划创建失败都会导致整个批量操作回滚，确保数据的强一致性。

## 接口信息

- **端点**: `POST /api/funding-plans/batch`
- **权限**: 需要 `BUDGET_CREATE` 权限
- **认证**: 需要 JWT 认证

## 请求格式

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <your_jwt_token>
```

### 请求体
```json
{
  "plans": [
    {
      "title": "资金计划标题",
      "year": 2024,
      "month": 12,
      "weekOfMonth": 1,
      "plannedAmount": 10000,
      "remarks": "备注信息（可选）",
      "budgetId": "预算ID"
    }
  ]
}
```

### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| plans | Array | ✅ | 资金计划数组，最少1个，最多50个 |
| plans[].title | String | ✅ | 计划标题，最大200字符 |
| plans[].year | Integer | ✅ | 年份，范围：2020-2050 |
| plans[].month | Integer | ✅ | 月份，范围：1-12 |
| plans[].weekOfMonth | Integer | ✅ | 当月第几周，范围：1-5 |
| plans[].plannedAmount | Number | ✅ | 计划金额，必须大于0 |
| plans[].remarks | String | ❌ | 备注信息 |
| plans[].budgetId | String | ✅ | 关联的预算ID |

## 响应格式

### 成功响应 (200)
```json
{
  "success": true,
  "data": {
    "createdPlans": [
      {
        "id": "funding_plan_id_1",
        "title": "资金计划1",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 1,
        "plannedAmount": 10000,
        "paidAmount": 0,
        "remainingAmount": 10000,
        "status": "draft",
        "approvalStatus": "none",
        "budgetId": "budget_id_1",
        "budget": {
          "id": "budget_id_1",
          "title": "营销预算",
          "contractAmount": 100000
        },
        "createdAt": "2024-12-05T10:00:00Z",
        "updatedAt": "2024-12-05T10:00:00Z"
      },
      {
        "id": "funding_plan_id_2",
        "title": "资金计划2",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 2,
        "plannedAmount": 15000,
        "paidAmount": 0,
        "remainingAmount": 15000,
        "status": "draft",
        "approvalStatus": "none",
        "budgetId": "budget_id_1",
        "createdAt": "2024-12-05T10:00:00Z",
        "updatedAt": "2024-12-05T10:00:00Z"
      }
    ],
    "totalCount": 2
  },
  "message": "成功批量创建 2 个资金计划"
}
```

### 错误响应

#### 400 - 请求参数验证失败
```json
{
  "success": false,
  "error": "请求参数验证失败",
  "message": "请求数据格式不正确",
  "details": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["plans", 0, "plannedAmount"],
      "message": "Expected number, received string"
    }
  ]
}
```

#### 400 - 预算不存在
```json
{
  "success": false,
  "error": "预算不存在",
  "message": "预算不存在：budgetId \"budget_invalid_id\" (计划索引: 0)"
}
```

#### 400 - 预算余额不足
```json
{
  "success": false,
  "error": "预算余额不足",
  "message": "预算余额不足：预算ID \"budget_id_1\"，可用金额 5000，需要 15000"
}
```

#### 400 - 资金计划冲突
```json
{
  "success": false,
  "error": "资金计划冲突",
  "message": "资金计划已存在：2024年12月第1周 (计划索引: 1)"
}
```

#### 400 - 批量数据重复
```json
{
  "success": false,
  "error": "资金计划冲突",
  "message": "批量创建中存在重复的年月周组合：2024年12月第1周 (计划索引: 2)"
}
```

#### 401 - 用户未认证
```json
{
  "error": "用户未认证"
}
```

#### 403 - 权限不足
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

## 业务规则

### 1. 严格事务模式 ⚠️
- **全成功或全失败**：任何一个资金计划创建失败，整个批量操作都会回滚
- **数据强一致性**：确保不会出现部分成功的情况
- **原子性操作**：所有验证和创建操作在同一个数据库事务中完成

### 2. 数据验证
- 每次最多创建50个资金计划
- 同一预算下的年月周组合必须唯一
- 批量创建中不能有重复的年月周组合
- 所有必填字段必须提供且格式正确

### 3. 预算验证
- 验证所有涉及的预算是否存在
- 检查预算可用金额是否足够支持所有计划
- 计算公式：可用金额 = 预算合同金额 - 已有资金计划总额
- 按预算分组验证，确保不超出预算限制

### 4. 冲突检查
- 检查数据库中是否已存在相同的年月周组合
- 检查批量数据内部是否有重复的年月周组合
- 提供精确的冲突位置信息

### 5. 错误处理
- 提供明确的错误类型和详细信息
- 包含失败计划的索引位置，便于定位问题
- 区分验证错误、业务逻辑错误和系统错误
- **不支持部分成功**：一个失败，全部回滚

## 使用示例

### cURL 示例
```bash
curl -X POST http://localhost:3000/api/funding-plans/batch \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "plans": [
      {
        "title": "Q4营销预算-第1周",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 1,
        "plannedAmount": 50000,
        "remarks": "年末营销活动预算",
        "budgetId": "budget_marketing_q4"
      },
      {
        "title": "Q4营销预算-第2周",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 2,
        "plannedAmount": 60000,
        "remarks": "双十二活动预算",
        "budgetId": "budget_marketing_q4"
      }
    ]
  }'
```

### JavaScript 示例
```javascript
const response = await fetch('/api/funding-plans/batch', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    plans: [
      {
        title: '技术开发预算-第1周',
        year: 2024,
        month: 12,
        weekOfMonth: 1,
        plannedAmount: 80000,
        budgetId: 'budget_tech_dev'
      }
    ]
  })
});

const result = await response.json();
console.log('创建结果:', result);
```

## 注意事项

1. **性能考虑**: 建议单次批量创建不超过20个计划
2. **并发控制**: 避免同时对同一预算进行批量操作
3. **错误恢复**: 失败的计划可以单独重试或修正后重新批量创建
4. **审计日志**: 所有创建操作都会记录审计日志
5. **权限检查**: 确保用户有足够的权限访问相关预算

## 相关接口

- `POST /api/funding-plans` - 单个创建资金计划
- `GET /api/funding-plans` - 获取资金计划列表
- `PUT /api/funding-plans/:id` - 更新资金计划
- `DELETE /api/funding-plans/:id` - 删除资金计划
