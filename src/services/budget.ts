import { db } from './database.js';
import { DingTalkNotificationService } from './dingtalkNotification.js';
import { UserSyncService } from './userSync.js';
import { DingTalkService } from './dingtalk.js';
import { ProjectService } from './project.js';
import { BrandService } from './brand.js';
import { Budget, ServiceType, TaxRate } from '../types/project.js';

// 保留周预算状态枚举以兼容现有代码
export enum BudgetStatus {
  DRAFT = 'created',           // 草稿
  CREATED = 'created',           // 草稿
  APPROVED = 'approved',     // 已批准
  EXECUTING = 'executing',   // 执行中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled',   // 已取消
}

// 创建周预算请求
export interface CreateBudgetRequest {
  title: string;
  // weekStartDate: Date | string;
  // weekEndDate: Date | string;
  serviceType: ServiceType;
  serviceContent: string;
  status?: BudgetStatus;
  remarks?: string;
  contractAmount: number;
  paidAmount?: number;
  taxRate: TaxRate;
  supplierId?: string;
}

// 周预算列表响应
export interface BudgetListResponse {
  budgets: Budget[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 预算查询参数
export interface BudgetQueryParams {
  page?: number;
  pageSize?: number;
  projectId?: string;
  supplierId?: string;
  serviceType?: ServiceType;
  status?: BudgetStatus;
  sortBy?: 'title' | 'contractAmount' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}


// 周预算统计信息 - 保留以兼容现有代码
export interface BudgetStats {
  totalBudgets: number;             // 总预算数
  totalContractAmount: number;      // 总合同金额
  totalPaidAmount: number;          // 总已付金额
  totalRemainingAmount: number;     // 总剩余金额
  budgetsByServiceType: Array<{
    serviceType: ServiceType;
    count: number;
    totalAmount: number;
  }>;
  budgetsByStatus: Array<{
    status: BudgetStatus;
    count: number;
    totalAmount: number;
  }>;
  budgetsBySupplier: Array<{
    supplierId: string;
    supplierName: string;
    count: number;
    totalAmount: number;
  }>;
  trend: Array<{
    week: string;
    contractAmount: number;
    paidAmount: number;
  }>;
}

// 更新周预算请求
export interface UpdateBudgetRequest extends Partial<CreateBudgetRequest> {
  id: string;
  status?: BudgetStatus;
  paidAmount?: number;
}

// 创建预算请求
export interface CreateBudgetRequest {
  title: string;
  serviceType: ServiceType;
  serviceContent: string;
  remarks?: string;
  contractAmount: number;
  taxRate: TaxRate;
  supplierId?: string;
}

// 更新预算请求
export interface UpdateBudgetRequest extends Partial<CreateBudgetRequest> {
  id: string;
  status?: BudgetStatus;
  totalPaidAmount?: number;
}

export class BudgetService {
  private projectService: ProjectService;
  private dingTalkService: DingTalkService;
  private userSyncService: UserSyncService;
  private notificationService: DingTalkNotificationService;
  private brandService: BrandService;

  constructor() {
    this.projectService = new ProjectService();
    this.brandService = new BrandService();
    this.dingTalkService = new DingTalkService();
    this.notificationService = new DingTalkNotificationService();
    this.userSyncService = new UserSyncService(db, this.dingTalkService);

  }

  /**
   * 根据预算id获取剩余可用的金额
   * @param budgetId 预算ID
   */
  getBudgetAvailableAmount(budgetId: string) {
    // 获取预算的总金额和已支付的金额
    return db.getBudgetAvailableAmount(budgetId);
  }

  /**
   * 批量验证预算可用金额
   * @param plans 资金计划数组
   * @returns 验证结果，包含每个预算的可用金额和验证状态
   */
  async validateBatchBudgetAvailability(plans: Array<{ budgetId: string; plannedAmount: number }>) {
    return db.validateBatchBudgetAvailability(plans);
  }


  // 预算管理方法

  // 创建预算
  async createBudget(projectId: string, request: CreateBudgetRequest, createdBy: string, isImportingData?: boolean): Promise<Budget> {
    const weeklyBudget = await db.createBudget(projectId, request, createdBy);

    // 异步检查周预算是否超过项目成本10%
    // 开发环境不发送通知
    if (process.env.NODE_ENV !== 'development' && !isImportingData) {
      this.checkBudgetExceeded(weeklyBudget, projectId).catch(error => {
        console.error('检查周预算超额失败:', error);
      });
    }

    return weeklyBudget;
  }

  // 获取周预算列表
  async getBudgets(params: BudgetQueryParams = {}): Promise<BudgetListResponse> {
    return await db.getBudgets(params);
  }

  // 获取单个周预算
  async getBudget(id: string): Promise<Budget | null> {
    return await db.getBudget(id);
  }

  // 更新周预算
  async updateBudget(request: UpdateBudgetRequest, updatedBy: string): Promise<Budget> {
    const weeklyBudget = await db.updateBudget(request, updatedBy);

    // 如果更新了合同金额，检查是否超过项目成本10%
    if (request.contractAmount !== undefined) {
      this.checkBudgetExceeded(weeklyBudget, weeklyBudget.projectId).catch(error => {
        console.error('检查周预算超额失败:', error);
      });
    }

    return weeklyBudget;
  }

  // 删除周预算
  async deleteBudget(id: string): Promise<boolean> {
    return await db.deleteBudget(id);
  }

  // 获取周预算统计
  async getBudgetStats(): Promise<BudgetStats> {
    return await db.getBudgetStats();
  }

  // 批量创建周预算
  async batchCreateBudgets(
    projectId: string,
    startDate: string,
    endDate: string,
    serviceType: ServiceType,
    defaultContractAmount: number,
    defaultTaxRate: TaxRate,
    createdBy: string
  ): Promise<Budget[]> {
    const weeklyBudgets = await db.batchCreateBudgets(projectId, startDate, endDate, serviceType, defaultContractAmount, defaultTaxRate, createdBy);

    // 异步检查每个周预算是否超过项目成本10%
    weeklyBudgets.forEach((budget: Budget) => {
      this.checkBudgetExceeded(budget, projectId).catch(error => {
        console.error('检查周预算超额失败:', error);
      });
    });

    return weeklyBudgets;

  }

  /**
   * 检查周预算是否超过项目成本10%并发送通知
   */
  async checkBudgetExceeded(budget: Budget, projectId: string): Promise<void> {
    try {
      // 获取项目信息
      const project = await this.projectService.getProject(projectId);
      if (!project) {
        console.warn(`无法获取项目信息: ${projectId}`);
        return;
      }

      // 计算项目总成本（包含居间费）
      const totalProjectCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost + project.cost.intermediaryCost;

      // 计算超额比例
      const exceedPercentage = (budget.contractAmount / totalProjectCost) * 100;

      // 如果超过10%，发送通知
      if (exceedPercentage > 10) {
        // 获取项目创建人用户信息
        const creatorInfo = await this.userSyncService.getUserInfo(project.createdBy);
        if (!creatorInfo) {
          console.warn(`无法获取项目创建人用户信息: ${project.createdBy}`);
          return;
        }

        // 获取品牌信息
        const brand = await this.brandService.getBrand(project.brandId);
        const brandName = brand?.name || '未知品牌';

        // 构建通知数据
        const notificationData = {
          projectName: project.projectName,
          brandName,
          weeklyBudgetTitle: budget.title,
          contractAmount: budget.contractAmount,
          projectCost: totalProjectCost,
          exceedPercentage,
          creatorName: creatorInfo.name
        };

        // 发送通知
        const success = await this.notificationService.sendBudgetExceededNotification(
          project.createdBy,
          notificationData
        );

        if (success) {
          console.log(`周预算超额通知发送成功: ${budget.title} -> ${creatorInfo.name}`);
        } else {
          console.error(`周预算超额通知发送失败: ${budget.title} -> ${creatorInfo.name}`);
        }
      }
    } catch (error) {
      console.error('检查周预算超额时出错:', error);
    }
  }
}