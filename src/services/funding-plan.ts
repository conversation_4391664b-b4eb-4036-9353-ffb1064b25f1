import {
  CreateFundingPlanRequest,
  FundingPlan,
  FundingPlanListResponse,
  FundingPlanQueryParams,
  FundingPlanStats,
  UpdateFundingPlanRequest
} from '../types/project.js';
import { db } from './database.js';

export class FundingPlanService {

  constructor() {
  }


  // 资金计划管理
  async createFundingPlan(data: CreateFundingPlanRequest, createdBy: string): Promise<FundingPlan> {
    return await db.createFundingPlan(data, createdBy);

  }

  async getFundingPlans(params: FundingPlanQueryParams = {}): Promise<FundingPlanListResponse> {
    return await db.getFundingPlans(params);

  }

  async getFundingPlan(id: string): Promise<FundingPlan | null> {
    return await db.getFundingPlan(id);
  }

  async updateFundingPlan(data: UpdateFundingPlanRequest, updatedBy: string): Promise<FundingPlan> {
    return await db.updateFundingPlan(data, updatedBy);

  }

  async deleteFundingPlan(id: string): Promise<boolean> {
    return await db.deleteFundingPlan(id);

  }

  // 批量创建资金计划 - 严格事务模式
  async createBatchFundingPlans(plans: CreateFundingPlanRequest[], createdBy: string): Promise<FundingPlan[]> {
    return await db.createBatchFundingPlans(plans, createdBy);

  }

  async getFundingPlanStats(): Promise<FundingPlanStats> {
    return await db.getFundingPlanStats();
  }
}
