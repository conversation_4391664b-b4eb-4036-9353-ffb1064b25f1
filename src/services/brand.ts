
import { Brand, BrandListResponse, BrandQueryParams } from '../types/project.js';
import { db } from './database.js';

// 品牌创建请求
export interface CreateBrandRequest {
  name: string;
  description?: string;
  logo?: string;
  status?: Brand['status'];
}

// 品牌更新请求
export interface UpdateBrandRequest extends Partial<CreateBrandRequest> {
  id: string;
  status?: Brand['status'];
}
export class BrandService {
  async createBrandsBatch(brands: Array<{ name: string; description?: string; logo?: string; status?: 'active' | 'inactive' }>, createdBy: string) {
    return await db.createBrandsBatch(brands, createdBy);
  }


  // 获取品牌列表
  async getBrands(params: BrandQueryParams = {}): Promise<BrandListResponse> {
    const dbResult = await db.getBrands(params);
    return {
      ...dbResult,
      brands: dbResult.brands.map(brand => this.transformDbBrand(brand))
    }
  }


  // 获取单个品牌
  async getBrand(id: string): Promise<Brand | null> {
    const dbBrand = await db.getBrand(id);
    return dbBrand ? this.transformDbBrand(dbBrand) : null;
  }

  // 更新品牌
  async updateBrand(request: UpdateBrandRequest): Promise<Brand> {
    const dbBrand = await db.updateBrand(request);
    return this.transformDbBrand(dbBrand);

  }

  // 删除品牌
  async deleteBrand(id: string): Promise<boolean> {
    return await db.deleteBrand(id);
  }


  // 转换数据库Brand对象为接口Brand
  private transformDbBrand(dbBrand: any): Brand {
    return {
      id: dbBrand.id,
      name: dbBrand.name,
      description: dbBrand.description || undefined,
      logo: dbBrand.logo || undefined,
      status: dbBrand.status.toLowerCase(),
      createdAt: dbBrand.createdAt,
      updatedAt: dbBrand.updatedAt,
      createdBy: dbBrand.createdBy
    };
  }


  // 创建品牌
  async createBrand(request: CreateBrandRequest, createdBy: string): Promise<Brand> {
    const dbBrand = await db.createBrand(request, createdBy);
    return this.transformDbBrand(dbBrand);
  }

}

