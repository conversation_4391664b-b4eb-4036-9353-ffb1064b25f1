import { FastifyInstance } from 'fastify';
import { BudgetController } from '../controllers/budget.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function budgetRoutes(fastify: FastifyInstance) {
  const budgetController = new BudgetController();

  // 预算管理路由

  // 创建项目预算
  fastify.post('/projects/:projectId/budgets', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_CREATE)],
    schema: {
      description: '创建项目预算',
      tags: ['Budget'],
      params: {
        type: 'object',
        required: ['projectId'],
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        }
      },
      body: {
        type: 'object',
        required: ['title', 'serviceType', 'serviceContent', 'contractAmount', 'taxRate'],
        properties: {
          title: { type: 'string', description: '预算标题' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          serviceContent: { type: 'string', description: '服务内容描述' },
          remarks: { type: 'string', description: '备注' },
          contractAmount: { type: 'number', minimum: 0, description: '合同金额' },
          taxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '税率'
          },
          supplierId: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.createBudget.bind(budgetController));

  // 获取预算列表
  fastify.get('/budgets', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取预算列表',
      tags: ['Budget'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
          projectId: { type: 'string', description: '项目ID' },
          supplierId: { type: 'string', description: '供应商ID' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          status: {
            type: 'string',
            enum: ['created', 'approved', 'executing', 'completed', 'cancelled'],
            description: '预算状态'
          },
          sortBy: {
            type: 'string',
            enum: ['title', 'contractAmount', 'createdAt'],
            default: 'createdAt',
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            default: 'desc',
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                budgets: { type: 'array' },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.getBudgets.bind(budgetController));

  // 获取项目预算列表
  fastify.get('/projects/:projectId/budgets', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取项目预算列表',
      tags: ['Budget'],
      params: {
        type: 'object',
        required: ['projectId'],
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
          supplierId: { type: 'string', description: '供应商ID' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          status: {
            type: 'string',
            enum: ['created', 'approved', 'executing', 'completed', 'cancelled'],
            description: '预算状态'
          },
          sortBy: {
            type: 'string',
            enum: ['title', 'contractAmount', 'createdAt'],
            default: 'createdAt',
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            default: 'desc',
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                budgets: { type: 'array' },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { projectId } = request.params as { projectId: string };
    const query = { ...(request.query as Record<string, any>), projectId };
    request.query = query;
    return budgetController.getBudgets(request, reply);
  });

  // 获取预算详情
  fastify.get('/budgets/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取预算详情',
      tags: ['Budget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '预算ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.getBudget.bind(budgetController));

  // 更新预算
  fastify.put('/budgets/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_UPDATE)],
    schema: {
      description: '更新预算',
      tags: ['Budget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '预算ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', description: '预算标题' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          serviceContent: { type: 'string', description: '服务内容描述' },
          remarks: { type: 'string', description: '备注' },
          contractAmount: { type: 'number', minimum: 0, description: '合同金额' },
          taxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '税率'
          },
          status: {
            type: 'string',
            enum: ['created', 'approved', 'executing', 'completed', 'cancelled'],
            description: '预算状态'
          },
          totalPaidAmount: { type: 'number', minimum: 0, description: '总已付金额' },
          supplierId: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.updateBudget.bind(budgetController));

  // 删除预算
  fastify.delete('/budgets/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_DELETE)],
    schema: {
      description: '删除预算',
      tags: ['Budget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '预算ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.deleteBudget.bind(budgetController));

  // 获取预算统计
  fastify.get('/budget-stats', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取预算统计',
      tags: ['Budget'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, budgetController.getBudgetStats.bind(budgetController));
}
