/**
 * 批量创建资金计划接口测试脚本
 * 使用方法：node test-batch-funding-plans.js
 */

const API_BASE_URL = 'http://localhost:3000/api';

// 模拟的测试数据
const testData = {
  plans: [
    {
      title: '测试资金计划1',
      year: 2024,
      month: 12,
      weekOfMonth: 1,
      plannedAmount: 10000,
      remarks: '第一个测试计划',
      budgetId: 'budget_test_id_1' // 需要替换为实际的预算ID
    },
    {
      title: '测试资金计划2',
      year: 2024,
      month: 12,
      weekOfMonth: 2,
      plannedAmount: 15000,
      remarks: '第二个测试计划',
      budgetId: 'budget_test_id_1' // 需要替换为实际的预算ID
    },
    {
      title: '测试资金计划3',
      year: 2024,
      month: 12,
      weekOfMonth: 3,
      plannedAmount: 20000,
      remarks: '第三个测试计划',
      budgetId: 'budget_test_id_2' // 需要替换为实际的预算ID
    }
  ]
};

async function testBatchCreateFundingPlans() {
  try {
    console.log('🚀 开始测试批量创建资金计划接口...\n');
    
    const response = await fetch(`${API_BASE_URL}/funding-plans/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：实际使用时需要添加认证头
        // 'Authorization': 'Bearer your_jwt_token_here'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log('📊 响应状态:', response.status);
    console.log('📋 响应数据:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('\n✅ 批量创建成功!');
      console.log(`   - 创建数量: ${result.data?.totalCount || 0} 个`);
      console.log(`   - 创建的计划ID: ${result.data?.createdPlans?.map(p => p.id).join(', ') || '无'}`);
    } else {
      console.log('\n❌ 批量创建失败!');
      console.log(`   - 错误类型: ${result.error || '未知错误'}`);
      console.log(`   - 错误信息: ${result.message || '无详细信息'}`);

      if (result.details && result.details.length > 0) {
        console.log('\n📋 验证错误详情:');
        result.details.forEach((detail, index) => {
          console.log(`   ${index + 1}. ${detail.path?.join('.')} - ${detail.message}`);
        });
      }
    }
    
  } catch (error) {
    console.error('🔥 测试过程中发生错误:', error.message);
  }
}

// 测试单个创建接口（对比用）
async function testSingleCreateFundingPlan() {
  try {
    console.log('\n🔄 测试单个创建接口（对比用）...\n');
    
    const singlePlan = {
      title: '单个测试资金计划',
      year: 2024,
      month: 12,
      weekOfMonth: 4,
      plannedAmount: 5000,
      remarks: '单个创建测试',
      budgetId: 'budget_test_id_1' // 需要替换为实际的预算ID
    };

    const response = await fetch(`${API_BASE_URL}/funding-plans`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：实际使用时需要添加认证头
        // 'Authorization': 'Bearer your_jwt_token_here'
      },
      body: JSON.stringify(singlePlan)
    });

    const result = await response.json();
    
    console.log('📊 单个创建响应状态:', response.status);
    console.log('📋 单个创建响应数据:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('🔥 单个创建测试错误:', error.message);
  }
}

// 运行测试
async function runTests() {
  console.log('🎯 批量创建资金计划接口测试\n');
  console.log('⚠️  注意：请确保：');
  console.log('   1. 服务器正在运行 (http://localhost:3000)');
  console.log('   2. 已替换测试数据中的 budgetId 为实际存在的预算ID');
  console.log('   3. 已添加有效的认证令牌\n');
  
  await testBatchCreateFundingPlans();
  await testSingleCreateFundingPlan();
  
  console.log('\n🏁 测试完成!');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testBatchCreateFundingPlans,
  testSingleCreateFundingPlan
};
