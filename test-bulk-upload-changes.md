# 批量上传预算修改验证

## 修改内容总结

### 1. 主要变更
- 将 `createWeeklyBudgetFromCSVData` 改为 `createBudgetFromCSVData`
- 添加了 `createFundingPlanFromCSVData` 方法
- 修改了 `createCompletedApprovalFromCSVData` 方法，针对资金计划而不是周预算
- 更新了批量上传主流程 `bulkUploadWeeklyBudgets`

### 2. 新的处理流程
```
CSV数据 -> 解析 -> 创建预算(Budget) -> 如果有已付金额 -> 创建资金计划(FundingPlan) -> 创建审批记录 -> 更新资金计划已付金额
```

### 3. 关键方法变更

#### createBudgetFromCSVData
- 输入：项目、供应商ID、CSV数据、创建者
- 输出：Budget对象
- 功能：创建预算记录，去掉了周的概念

#### createFundingPlanFromCSVData  
- 输入：预算、CSV数据、创建者
- 输出：FundingPlan对象
- 功能：根据当前时间计算年月周，创建资金计划

#### createCompletedApprovalFromCSVData
- 输入：资金计划ID、CSV数据、创建者
- 输出：审批记录
- 功能：创建已完成的审批记录，并更新资金计划已付金额

#### updateFundingPlanPaidAmount
- 输入：资金计划ID、已付金额
- 功能：更新资金计划的已付金额，剩余金额自动计算

### 4. 数据流验证

#### 输入CSV数据结构
```typescript
interface WeeklyBudgetCSVData {
  projectName: string;
  supplierName: string;
  serviceType: string;
  serviceContent: string;
  contractAmount: number;
  taxRate: TaxRate;
  taxExclusiveAmount: number;
  paidAmount: number;
  unpaidAmount: number;
}
```

#### 处理步骤
1. **创建预算**：
   - 标题：`${supplierName}-${serviceContent}`
   - 合同金额：`contractAmount`
   - 服务类型、内容、税率等基本信息

2. **创建资金计划**（如果paidAmount > 0）：
   - 标题：`${supplierName}-${month}月第${weekOfMonth}周-${serviceContent}`
   - 计划金额：等于已付金额
   - 年月周：根据当前时间计算

3. **创建审批记录**：
   - 状态：APPROVED
   - 审批金额：等于已付金额
   - 关联资金计划ID

4. **更新已付金额**：
   - 将已付金额写回资金计划
   - 自动计算剩余金额

### 5. 预期结果
- 每行CSV数据创建一个Budget记录
- 如果有已付金额，创建对应的FundingPlan记录
- 创建已完成的审批记录，状态为APPROVED
- 资金计划的已付金额正确更新
- 剩余金额自动计算为：计划金额 - 已付金额

### 6. 兼容性考虑
- 保持了原有的CSV格式不变
- 保持了原有的API接口不变
- 只是内部处理逻辑从周预算改为预算+资金计划

## 测试建议

1. **单元测试**：测试各个新方法的功能
2. **集成测试**：测试完整的CSV上传流程
3. **数据验证**：确保创建的记录数据正确
4. **边界测试**：测试无已付金额的情况
5. **错误处理**：测试各种异常情况
