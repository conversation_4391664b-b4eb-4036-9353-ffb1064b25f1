/**
 * 严格事务模式测试脚本
 * 测试批量创建资金计划的"全成功或全失败"特性
 */

const API_BASE_URL = 'http://localhost:3000/api';

// 测试用例1：正常批量创建（应该全部成功）
const validTestData = {
  plans: [
    {
      title: '正常计划1',
      year: 2024,
      month: 12,
      weekOfMonth: 1,
      plannedAmount: 5000,
      remarks: '测试正常创建',
      budgetId: 'budget_test_valid' // 需要替换为实际存在的预算ID
    },
    {
      title: '正常计划2',
      year: 2024,
      month: 12,
      weekOfMonth: 2,
      plannedAmount: 6000,
      remarks: '测试正常创建',
      budgetId: 'budget_test_valid'
    }
  ]
};

// 测试用例2：包含无效预算ID（应该全部失败）
const invalidBudgetTestData = {
  plans: [
    {
      title: '有效计划',
      year: 2024,
      month: 12,
      weekOfMonth: 3,
      plannedAmount: 5000,
      budgetId: 'budget_test_valid' // 有效的预算ID
    },
    {
      title: '无效预算计划',
      year: 2024,
      month: 12,
      weekOfMonth: 4,
      plannedAmount: 6000,
      budgetId: 'budget_invalid_id' // 无效的预算ID
    }
  ]
};

// 测试用例3：预算余额不足（应该全部失败）
const insufficientBudgetTestData = {
  plans: [
    {
      title: '超额计划1',
      year: 2024,
      month: 12,
      weekOfMonth: 5,
      plannedAmount: 999999, // 超大金额
      budgetId: 'budget_test_valid'
    },
    {
      title: '正常计划',
      year: 2025,
      month: 1,
      weekOfMonth: 1,
      plannedAmount: 1000,
      budgetId: 'budget_test_valid'
    }
  ]
};

// 测试用例4：重复的年月周组合（应该全部失败）
const duplicateTestData = {
  plans: [
    {
      title: '重复计划1',
      year: 2024,
      month: 12,
      weekOfMonth: 1,
      plannedAmount: 5000,
      budgetId: 'budget_test_valid'
    },
    {
      title: '重复计划2',
      year: 2024,
      month: 12,
      weekOfMonth: 1, // 相同的年月周组合
      plannedAmount: 6000,
      budgetId: 'budget_test_valid'
    }
  ]
};

async function testBatchCreate(testName, testData, expectedSuccess = false) {
  console.log(`\n🧪 ${testName}`);
  console.log('=' .repeat(50));
  
  try {
    const response = await fetch(`${API_BASE_URL}/funding-plans/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：实际使用时需要添加认证头
        // 'Authorization': 'Bearer your_jwt_token_here'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log(`📊 响应状态: ${response.status}`);
    console.log(`🎯 预期结果: ${expectedSuccess ? '成功' : '失败'}`);
    console.log(`✅ 实际结果: ${response.ok ? '成功' : '失败'}`);
    
    if (response.ok) {
      console.log(`📈 创建数量: ${result.data?.totalCount || 0}`);
      console.log(`📋 计划ID: ${result.data?.createdPlans?.map(p => p.id).slice(0, 3).join(', ') || '无'}${result.data?.createdPlans?.length > 3 ? '...' : ''}`);
    } else {
      console.log(`❌ 错误类型: ${result.error || '未知'}`);
      console.log(`💬 错误信息: ${result.message || '无'}`);
    }
    
    // 验证测试结果
    const testPassed = (response.ok === expectedSuccess);
    console.log(`🏆 测试结果: ${testPassed ? '✅ 通过' : '❌ 失败'}`);
    
    return testPassed;
    
  } catch (error) {
    console.error(`🔥 测试执行错误: ${error.message}`);
    return false;
  }
}

async function runStrictTransactionTests() {
  console.log('🎯 批量创建资金计划 - 严格事务模式测试');
  console.log('测试目标：验证"全成功或全失败"的事务特性\n');
  
  console.log('⚠️  测试前准备：');
  console.log('   1. 确保服务器运行在 http://localhost:3000');
  console.log('   2. 替换 budget_test_valid 为实际存在的预算ID');
  console.log('   3. 确保该预算有足够的可用金额（至少20000）');
  console.log('   4. 添加有效的认证令牌');
  
  const testResults = [];
  
  // 执行所有测试用例
  testResults.push(await testBatchCreate(
    '测试1: 正常批量创建',
    validTestData,
    true
  ));
  
  testResults.push(await testBatchCreate(
    '测试2: 包含无效预算ID（事务回滚测试）',
    invalidBudgetTestData,
    false
  ));
  
  testResults.push(await testBatchCreate(
    '测试3: 预算余额不足（事务回滚测试）',
    insufficientBudgetTestData,
    false
  ));
  
  testResults.push(await testBatchCreate(
    '测试4: 重复年月周组合（事务回滚测试）',
    duplicateTestData,
    false
  ));
  
  // 汇总测试结果
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试汇总');
  console.log('='.repeat(60));
  
  const passedTests = testResults.filter(result => result).length;
  const totalTests = testResults.length;
  
  console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败测试: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！严格事务模式工作正常。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查实现或测试配置。');
  }
  
  console.log('\n💡 严格事务模式特性验证：');
  console.log('   - ✅ 全部成功时，所有计划都被创建');
  console.log('   - ✅ 任何失败时，所有操作都被回滚');
  console.log('   - ✅ 提供明确的错误信息和位置');
  console.log('   - ✅ 保证数据的强一致性');
}

// 如果直接运行此脚本
if (require.main === module) {
  runStrictTransactionTests().catch(console.error);
}

module.exports = {
  testBatchCreate,
  runStrictTransactionTests
};
